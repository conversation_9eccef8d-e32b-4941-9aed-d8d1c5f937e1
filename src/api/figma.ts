/**
 * Figma API 封装 (Electron版本)
 * 通过IPC与主进程通信来处理HTTP请求
 */

// 声明全局的ipcRenderer类型
declare global {
  interface Window {
    ipcRenderer: {
      invoke(channel: string, ...args: any[]): Promise<any>;
    };
  }
}

// Figma API 响应类型定义
export interface FigmaNode {
  id: string;
  name: string;
  type: string;
  visible?: boolean;
  locked?: boolean;
  exportSettings?: any[];
  blendMode?: string;
  preserveRatio?: boolean;
  constraints?: any;
  layoutAlign?: string;
  layoutGrow?: number;
  layoutSizingHorizontal?: string;
  layoutSizingVertical?: string;
  transitionNodeID?: string;
  transitionDuration?: number;
  transitionEasing?: string;
  opacity?: number;
  absoluteBoundingBox?: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
  absoluteRenderBounds?: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
  effects?: any[];
  size?: {
    x: number;
    y: number;
  };
  relativeTransform?: number[][];
  isMask?: boolean;
  fills?: any[];
  fillGeometry?: any[];
  strokes?: any[];
  strokeWeight?: number;
  strokeAlign?: string;
  strokeGeometry?: any[];
  cornerRadius?: number;
  cornerSmoothing?: number;
  children?: FigmaNode[];
  characters?: string;
  style?: any;
  layoutVersion?: number;
  characterStyleOverrides?: any[];
  styleOverrideTable?: any;
  lineTypes?: string[];
  lineIndentations?: number[];
}

export interface FigmaNodesResponse {
  name: string;
  role: string;
  lastModified: string;
  editorType: string;
  thumbnailUrl: string;
  version: string;
  nodes: {
    [nodeId: string]: {
      document: FigmaNode;
      components: any;
      componentSets: any;
      schemaVersion: number;
      styles: any;
    };
  };
  err?: string;
}

// Figma 链接解析结果
export interface FigmaLinkInfo {
  fileKey: string;
  nodeId?: string;
  originalUrl: string;
}

/**
 * 解析 Figma 分享链接，提取文件 key 和节点 ID
 * 通过IPC调用主进程中的解析函数
 */
export async function parseFigmaUrl(url: string): Promise<FigmaLinkInfo> {
  try {
    const result = await window.ipcRenderer.invoke('parse-figma-url', url);

    if (!result.success) {
      throw new Error(result.error);
    }

    return result.data;
  } catch (error) {
    throw new Error(`解析 Figma 链接失败: ${error instanceof Error ? error.message : '未知错误'}`);
  }
}

/**
 * 获取 Figma 文件中指定节点的信息
 * @param fileKey Figma 文件的 key
 * @param nodeIds 节点 ID 数组，格式如 ['46-6', '47-8']
 * @returns Promise<FigmaNodesResponse>
 */
export async function getFigmaNodes(fileKey: string, nodeIds: string[]): Promise<FigmaNodesResponse> {
  const url = `https://api.figma.com/v1/files/${fileKey}/nodes`;

  try {
    const result = await window.ipcRenderer.invoke('figma-api-request', {
      url,
      params: {
        ids: nodeIds.join(',')
      }
    });

    if (!result.success) {
      throw new Error(result.error);
    }

    if (result.data.err) {
      throw new Error(`Figma API 错误: ${result.data.err}`);
    }

    return result.data;
  } catch (error) {
    if (error instanceof Error) {
      throw new Error(`获取 Figma 节点信息失败: ${error.message}`);
    }
    throw new Error('获取 Figma 节点信息失败: 未知错误');
  }
}

/**
 * 根据 Figma 分享链接获取文件信息
 * @param figmaUrl Figma 分享链接
 * @returns Promise<FigmaNodesResponse>
 */
export async function getFigmaInfoFromUrl(figmaUrl: string): Promise<FigmaNodesResponse> {
  // 解析链接
  const linkInfo = await parseFigmaUrl(figmaUrl);

  // 如果有节点 ID，获取指定节点信息
  if (linkInfo.nodeId) {
    return await getFigmaNodes(linkInfo.fileKey, [linkInfo.nodeId]);
  }

  // 如果没有节点 ID，获取文件基本信息
  try {
    const fileInfoUrl = `https://api.figma.com/v1/files/${linkInfo.fileKey}`;
    const result = await window.ipcRenderer.invoke('figma-api-request', {
      url: fileInfoUrl
    });

    if (!result.success) {
      throw new Error(result.error);
    }

    // 返回文件基本信息
    return {
      name: result.data.name || 'Unknown File',
      role: 'viewer',
      lastModified: result.data.lastModified || '',
      editorType: 'figma',
      thumbnailUrl: result.data.thumbnailUrl || '',
      version: result.data.version || '0',
      nodes: {}
    };
  } catch (error) {
    throw new Error(`获取 Figma 文件信息失败: ${error instanceof Error ? error.message : '未知错误'}`);
  }
}

/**
 * 打印 Figma 文件信息到控制台
 * @param figmaUrl Figma 分享链接
 */
export async function printFigmaInfo(figmaUrl: string): Promise<void> {
  try {
    console.log('🔍 正在解析 Figma 链接...');
    const linkInfo = await parseFigmaUrl(figmaUrl);

    console.log('📋 链接解析结果:');
    console.log(JSON.stringify(linkInfo, null, 2));
    console.log('');

    console.log('📡 正在获取 Figma 文件信息...');
    const figmaInfo = await getFigmaInfoFromUrl(figmaUrl);

    console.log('✅ 获取成功！完整文件信息 JSON:');
    console.log(JSON.stringify(figmaInfo, null, 2));

    console.log('');
    console.log('🎉 Figma 文件信息获取完成！');

  } catch (error) {
    console.error('❌ 获取 Figma 文件信息失败:');
    console.error(`   ${error instanceof Error ? error.message : '未知错误'}`);
    throw error;
  }
}

// 导出所有功能
export default {
  parseFigmaUrl,
  getFigmaNodes,
  getFigmaInfoFromUrl,
  printFigmaInfo
};
